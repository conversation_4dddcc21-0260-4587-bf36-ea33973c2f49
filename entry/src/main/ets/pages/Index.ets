import { Trip, SAMPLE_TRIPS, TripDataManager } from '../models/TripModel';
import { TripCard } from '../components/TripCard';
import { BottomNavigation } from '../components/BottomNavigation';
import { SearchAndFilter } from '../components/SearchAndFilter';
import { EmptyState, EmptyStateType } from '../components/EmptyState';
import { FloatingActionButton } from '../components/FloatingActionButton';
import { THEME_COLORS } from '../utils/TripUtils';

@Entry
@Component
struct Index {
  @State tripManager: TripDataManager = new TripDataManager();
  @State searchText: string = '';
  @State selectedFilter: string = 'all';
  @State currentTabIndex: number = 1; // 默认选中"行程"标签
  @State isRefreshing: boolean = false;

  filteredTrips(): Trip[] {
    let filtered = this.tripManager.getAllTrips();

    // 按搜索文本过滤
    if (this.searchText) {
      filtered = this.tripManager.searchTrips(this.searchText);
    }

    // 按状态过滤
    if (this.selectedFilter !== 'all') {
      filtered = filtered.filter(trip => trip.status === this.selectedFilter);
    }

    return filtered;
  }

  // 刷新数据
  refreshData() {
    this.isRefreshing = true;
    // 模拟网络请求
    setTimeout(() => {
      this.isRefreshing = false;
      console.log('数据刷新完成');
    }, 1000);
  }

  // 清除搜索和筛选
  clearFilters() {
    this.searchText = '';
    this.selectedFilter = 'all';
  }

  // 获取筛选后的行程数量统计
  getFilterStats(): string {
    const total = this.tripManager.getAllTrips().length;
    const filtered = this.filteredTrips().length;
    if (this.searchText || this.selectedFilter !== 'all') {
      return `显示 ${filtered} / ${total} 个行程`;
    }
    return `共 ${total} 个行程`;
  }

  // 处理搜索文本变化
  handleSearchChange = (text: string) => {
    this.searchText = text;
  }

  // 处理筛选变化
  handleFilterChange = (filter: string) => {
    this.selectedFilter = filter;
  }

  // 处理标签页切换
  handleTabChange = (index: number, title: string) => {
    this.currentTabIndex = index;
    console.log(`切换到标签: ${title}`);
  }

  // 处理行程点击
  handleTripClick = (trip: Trip) => {
    console.log(`点击了行程: ${trip.title}`);
  }

  // 处理创建行程
  handleCreateTrip = () => {
    console.log('创建新行程');
  }

  // 获取空状态类型
  getEmptyStateType(): EmptyStateType {
    if (this.searchText) {
      return EmptyStateType.NO_SEARCH_RESULTS;
    }
    if (this.selectedFilter !== 'all') {
      return EmptyStateType.NO_FILTER_RESULTS;
    }
    return EmptyStateType.NO_TRIPS;
  }


  build() {
    Stack() {
      Column() {
        // Header with title and search
        Column() {
          // Title
          Text('我的行程')
            .fontSize(24)
            .fontWeight(700)
            .fontColor(THEME_COLORS.textPrimary)
            .width('100%')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 16 })

          // Search and Filter Component
          SearchAndFilter({
            searchText: this.searchText,
            selectedFilter: this.selectedFilter,
            onSearchChange: this.handleSearchChange,
            onFilterChange: this.handleFilterChange,
            onClearFilters: this.clearFilters
          })

          // Stats Row
          Row() {
            Text(this.getFilterStats())
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)

            Blank()

            if (this.searchText || this.selectedFilter !== 'all') {
              Button('清除')
                .fontSize(12)
                .fontColor(THEME_COLORS.primary)
                .backgroundColor(Color.Transparent)
                .border({ width: 0 })
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                .onClick(() => {
                  this.clearFilters();
                })
            }
          }
          .width('100%')
          .margin({ bottom: 8 })
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 16, bottom: 16 })
        .backgroundColor(THEME_COLORS.cardBackground)

        // Trip List or Empty State
        if (this.filteredTrips().length === 0) {
          EmptyState({
            type: this.getEmptyStateType(),
            searchText: this.searchText,
            hasFilters: this.selectedFilter !== 'all',
            onCreateTrip: this.handleCreateTrip,
            onClearFilters: this.clearFilters
          })
        } else {
          Refresh({ refreshing: $$this.isRefreshing, offset: 120, friction: 100 }) {
            List({ space: 12 }) {
              ForEach(this.filteredTrips(), (trip: Trip) => {
                ListItem() {
                  TripCard({
                    trip: trip,
                    onTripClick: this.handleTripClick
                  })
                }
              })
            }
            .width('100%')
            .layoutWeight(1)
            .padding({ left: 16, right: 16, top: 8, bottom: 100 })
            .backgroundColor(THEME_COLORS.background)
          }
          .onStateChange((refreshStatus: RefreshStatus) => {
            console.log('刷新状态: ' + refreshStatus);
          })
          .onRefreshing(() => {
            this.refreshData();
          })
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(THEME_COLORS.background)

      // Bottom Navigation Bar
      BottomNavigation({
        currentTabIndex: this.currentTabIndex,
        onTabChange: this.handleTabChange
      })

      // Floating Add Button
      FloatingActionButton({
        onButtonClick: this.handleCreateTrip
      })
    }
    .width('100%')
    .height('100%')
    .alignContent(Alignment.TopStart)
  }

}
