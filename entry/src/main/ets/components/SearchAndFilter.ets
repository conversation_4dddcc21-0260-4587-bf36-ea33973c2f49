/**
 * 搜索和筛选组件
 * 提供搜索和筛选功能的UI组件
 */

import { getStatusLabel, THEME_COLORS } from '../utils/TripUtils';

// 筛选选项接口
export interface FilterOption {
  value: string;
  label: string;
}

// 默认筛选选项
export const DEFAULT_FILTER_OPTIONS: FilterOption[] = [
  { value: 'all', label: '全部' },
  { value: 'upcoming', label: '即将开始' },
  { value: 'in-progress', label: '进行中' },
  { value: 'completed', label: '已完成' }
];

@Component
export struct SearchAndFilter {
  @Prop searchText: string = '';
  @Prop selectedFilter: string = 'all';
  @Prop filterOptions: FilterOption[] = DEFAULT_FILTER_OPTIONS;
  @State showFilterDialog: boolean = false;
  
  onSearchChange?: (text: string) => void;
  onFilterChange?: (filter: string) => void;
  onClearFilters?: () => void;

  @Builder
  buildFilterOption(option: FilterOption) {
    Row() {
      Radio({ value: option.value, group: 'filterGroup' })
        .checked(this.selectedFilter === option.value)
        .onChange((isChecked: boolean) => {
          if (isChecked && this.onFilterChange) {
            this.onFilterChange(option.value);
          }
        })
        .radioStyle({
          checkedBackgroundColor: THEME_COLORS.primary,
          uncheckedBorderColor: '#d1d5db'
        })

      Text(option.label)
        .fontSize(16)
        .fontColor(THEME_COLORS.textPrimary)
        .margin({ left: 12 })
    }
    .width('100%')
    .height(40)
    .alignItems(VerticalAlign.Center)
    .onClick(() => {
      if (this.onFilterChange) {
        this.onFilterChange(option.value);
      }
    })
  }

  @Builder
  buildFilterSheet() {
    Column() {
      Text('筛选行程')
        .fontSize(18)
        .fontWeight(600)
        .fontColor(THEME_COLORS.textPrimary)
        .margin({ bottom: 20 })

      Text('按状态筛选')
        .fontSize(16)
        .fontWeight(500)
        .fontColor(THEME_COLORS.textPrimary)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 12 })

      // Filter Options
      Column({ space: 8 }) {
        ForEach(this.filterOptions, (option: FilterOption) => {
          this.buildFilterOption(option)
        })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)

      Blank()

      // Apply Button
      Button('应用筛选')
        .width('100%')
        .height(44)
        .backgroundColor(THEME_COLORS.primary)
        .borderRadius(12)
        .fontColor(Color.White)
        .fontSize(16)
        .fontWeight(600)
        .onClick(() => {
          this.showFilterDialog = false;
        })
        .margin({ top: 20 })
    }
    .width('100%')
    .padding(20)
    .backgroundColor(THEME_COLORS.cardBackground)
  }

  build() {
    Column() {
      // Search and Filter Row
      Row() {
        // Search Bar
        Row() {
          Image($r('sys.symbol.magnifyingglass'))
            .width(16)
            .height(16)
            .fillColor(THEME_COLORS.secondary)
            .margin({ left: 12 })

          TextInput({ placeholder: '搜索行程...', text: this.searchText })
            .backgroundColor(Color.Transparent)
            .border({ width: 0 })
            .fontSize(16)
            .fontColor(THEME_COLORS.textPrimary)
            .layoutWeight(1)
            .onChange((value: string) => {
              if (this.onSearchChange) {
                this.onSearchChange(value);
              }
            })
        }
        .width('100%')
        .height(40)
        .backgroundColor('#f2f2f7')
        .borderRadius(12)
        .alignItems(VerticalAlign.Center)
        .layoutWeight(1)

        // Filter Button
        Button() {
          Image($r('sys.symbol.line_3_horizontal_decrease'))
            .width(16)
            .height(16)
            .fillColor(THEME_COLORS.primary)
        }
        .width(40)
        .height(40)
        .backgroundColor(THEME_COLORS.primaryLight)
        .borderRadius(8)
        .margin({ left: 12 })
        .onClick(() => {
          this.showFilterDialog = true;
        })
      }
      .width('100%')
      .margin({ bottom: 8 })

      // Filter Tags
      if (this.selectedFilter !== 'all') {
        Row() {
          Text(`筛选: ${getStatusLabel(this.selectedFilter)}`)
            .fontSize(12)
            .fontColor(THEME_COLORS.primary)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .backgroundColor(THEME_COLORS.primaryLight)
            .borderRadius(12)

          Button('清除')
            .fontSize(12)
            .fontColor(THEME_COLORS.secondary)
            .backgroundColor(Color.Transparent)
            .border({ width: 0 })
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .onClick(() => {
              if (this.onFilterChange) {
                this.onFilterChange('all');
              }
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
        .margin({ bottom: 8 })
      }
    }
    .width('100%')
    .bindSheet($$this.showFilterDialog, this.buildFilterSheet(), {
      height: 300,
      showClose: true,
      dragBar: true
    })
  }
}
