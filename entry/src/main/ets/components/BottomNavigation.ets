/**
 * 底部导航栏组件
 * 提供应用的主要导航功能
 */

import { THEME_COLORS } from '../utils/TripUtils';

// 导航项接口
export interface NavItem {
  index: number;
  title: string;
  icon: Resource;
}

// 默认导航项
export const DEFAULT_NAV_ITEMS: NavItem[] = [
  { index: 0, title: '首页', icon: $r('sys.symbol.house') },
  { index: 1, title: '行程', icon: $r('sys.symbol.calendar') },
  { index: 2, title: '发现', icon: $r('sys.symbol.magnifyingglass') },
  { index: 3, title: '收藏', icon: $r('sys.symbol.heart') },
  { index: 4, title: '我的', icon: $r('sys.symbol.person') }
];

@Component
export struct BottomNavigation {
  @Prop currentTabIndex: number = 1;
  @Prop navItems: NavItem[] = DEFAULT_NAV_ITEMS;
  onTabChange?: (index: number, title: string) => void;

  @Builder
  buildTabItem(item: NavItem) {
    Column() {
      Image(item.icon)
        .width(24)
        .height(24)
        .fillColor(this.currentTabIndex === item.index ? THEME_COLORS.primary : THEME_COLORS.secondary)

      Text(item.title)
        .fontSize(12)
        .fontColor(this.currentTabIndex === item.index ? THEME_COLORS.primary : THEME_COLORS.secondary)
        .margin({ top: 4 })
    }
    .width(60)
    .height(60)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      if (this.onTabChange) {
        this.onTabChange(item.index, item.title);
      } else {
        console.log(`切换到标签: ${item.title}`);
      }
    })
  }

  build() {
    Row() {
      ForEach(this.navItems, (item: NavItem) => {
        this.buildTabItem(item)
      })
    }
    .width('100%')
    .height(80)
    .backgroundColor(THEME_COLORS.cardBackground)
    .justifyContent(FlexAlign.SpaceEvenly)
    .alignItems(VerticalAlign.Center)
    .position({ x: 0, y: '100%' })
    .translate({ x: 0, y: -80 })
    .border({ width: { top: 0.5 }, color: THEME_COLORS.border })
  }
}
