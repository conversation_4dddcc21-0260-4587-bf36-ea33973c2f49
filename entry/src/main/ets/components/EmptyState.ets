/**
 * 空状态组件
 * 用于显示无数据时的空状态界面
 */

import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

// 空状态类型枚举
export enum EmptyStateType {
  NO_TRIPS = 'no_trips',
  NO_SEARCH_RESULTS = 'no_search_results',
  NO_FILTER_RESULTS = 'no_filter_results'
}

@Component
export struct EmptyState {
  @Prop type: EmptyStateType = EmptyStateType.NO_TRIPS;
  @Prop searchText: string = '';
  @Prop hasFilters: boolean = false;
  onCreateTrip?: () => void;
  onClearFilters?: () => void;

  // 获取空状态的标题
  private getTitle(): string {
    switch (this.type) {
      case EmptyStateType.NO_SEARCH_RESULTS:
        return '没有找到相关行程';
      case EmptyStateType.NO_FILTER_RESULTS:
        return '没有符合条件的行程';
      case EmptyStateType.NO_TRIPS:
      default:
        return '还没有行程';
    }
  }

  // 获取空状态的描述
  private getDescription(): string {
    switch (this.type) {
      case EmptyStateType.NO_SEARCH_RESULTS:
        return '试试其他关键词或清除筛选条件';
      case EmptyStateType.NO_FILTER_RESULTS:
        return '试试调整筛选条件或清除所有筛选';
      case EmptyStateType.NO_TRIPS:
      default:
        return '创建您的第一个行程，开始规划美好的旅程吧！';
    }
  }

  // 获取空状态的图标
  private getIconText(): string {
    switch (this.type) {
      case EmptyStateType.NO_SEARCH_RESULTS:
        return IconConstants.EMPTY_SEARCH;
      case EmptyStateType.NO_FILTER_RESULTS:
        return IconConstants.EMPTY_FILTER;
      case EmptyStateType.NO_TRIPS:
      default:
        return IconConstants.EMPTY_DATA;
    }
  }

  // 是否显示创建按钮
  private shouldShowCreateButton(): boolean {
    return this.type === EmptyStateType.NO_TRIPS;
  }

  // 是否显示清除筛选按钮
  private shouldShowClearButton(): boolean {
    return this.type === EmptyStateType.NO_SEARCH_RESULTS || 
           this.type === EmptyStateType.NO_FILTER_RESULTS;
  }

  build() {
    Column() {
      // Empty State Icon
      IconText({
        iconText: this.getIconText(),
        fontSize: 60,
        fontColor: '#c7c7cc',
        width: 80,
        height: 80
      })
        .margin({ bottom: 16 })

      // Title
      Text(this.getTitle())
        .fontSize(18)
        .fontWeight(600)
        .fontColor(THEME_COLORS.secondary)
        .margin({ bottom: 8 })

      // Description
      Text(this.getDescription())
        .fontSize(14)
        .fontColor('#c7c7cc')
        .textAlign(TextAlign.Center)
        .lineHeight(20)
        .margin({ bottom: 24 })
        .padding({ left: 16, right: 16 })

      // Action Buttons
      Column({ space: 12 }) {
        // Create Trip Button
        if (this.shouldShowCreateButton()) {
          Button('创建行程')
            .width(120)
            .height(40)
            .backgroundColor(THEME_COLORS.primary)
            .borderRadius(20)
            .fontColor(Color.White)
            .fontSize(14)
            .fontWeight(600)
            .onClick(() => {
              if (this.onCreateTrip) {
                this.onCreateTrip();
              } else {
                console.log('创建新行程');
              }
            })
        }

        // Clear Filters Button
        if (this.shouldShowClearButton()) {
          Button('清除筛选')
            .width(120)
            .height(40)
            .backgroundColor(Color.Transparent)
            .border({ width: 1, color: THEME_COLORS.primary })
            .borderRadius(20)
            .fontColor(THEME_COLORS.primary)
            .fontSize(14)
            .fontWeight(600)
            .onClick(() => {
              if (this.onClearFilters) {
                this.onClearFilters();
              } else {
                console.log('清除筛选条件');
              }
            })
        }
      }
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .padding({ left: 32, right: 32, bottom: 100 })
    .backgroundColor(THEME_COLORS.background)
  }
}
